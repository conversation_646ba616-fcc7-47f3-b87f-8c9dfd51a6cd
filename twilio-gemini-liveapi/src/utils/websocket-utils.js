/**
 * WebSocket Utilities
 * Standardizes WebSocket property access across different flow handlers
 * Fixes inconsistent property naming issues identified in audit
 */

/**
 * Get the WebSocket connection from connection data
 * Handles various property names used across different handlers
 */
export function getWebSocket(connectionData) {
    if (!connectionData) {
        return null;
    }
    
    // Try different property names in order of preference
    return connectionData.ws || 
           connectionData.localWs || 
           connectionData.twilioWs ||
           connectionData.socket ||
           connectionData.connection?.socket ||
           connectionData.connection ||
           null;
}

/**
 * Get the Twilio WebSocket specifically
 */
export function getTwilioWebSocket(connectionData) {
    if (!connectionData) {
        return null;
    }
    
    return connectionData.twilioWs || 
           connectionData.ws ||
           connectionData.socket ||
           null;
}

/**
 * Get the local testing WebSocket specifically
 */
export function getLocalWebSocket(connectionData) {
    if (!connectionData) {
        return null;
    }
    
    return connectionData.localWs || 
           connectionData.ws ||
           connectionData.socket ||
           null;
}

/**
 * Standardize connection data properties
 * Ensures all handlers use consistent property names
 */
export function standardizeConnectionData(connectionData, ws, sessionType = 'unknown') {
    if (!connectionData || !ws) {
        return connectionData;
    }
    
    // Set standardized WebSocket properties
    connectionData.ws = ws; // Primary WebSocket reference
    
    // Set type-specific references for backward compatibility
    if (sessionType === 'twilio_call' || sessionType.includes('twilio')) {
        connectionData.twilioWs = ws;
    } else if (sessionType === 'local_test' || sessionType.includes('local')) {
        connectionData.localWs = ws;
    }
    
    // Ensure bounded arrays are initialized
    if (!Array.isArray(connectionData.conversationLog)) {
        connectionData.conversationLog = [];
    }
    if (!Array.isArray(connectionData.fullTranscript)) {
        connectionData.fullTranscript = [];
    }
    if (!Array.isArray(connectionData.speechTranscript)) {
        connectionData.speechTranscript = [];
    }
    
    // Add memory management properties
    connectionData.maxConversationLogSize = connectionData.maxConversationLogSize || 500;
    connectionData.maxTranscriptSize = connectionData.maxTranscriptSize || 1000;
    connectionData.maxSpeechTranscriptSize = connectionData.maxSpeechTranscriptSize || 1000;
    
    return connectionData;
}

/**
 * Check if WebSocket is ready for communication
 */
export function isWebSocketReady(ws) {
    return ws && ws.readyState === 1; // WebSocket.OPEN
}

/**
 * Safely send data to WebSocket
 */
export function safeSendWebSocket(ws, data, sessionId = 'unknown') {
    if (!isWebSocketReady(ws)) {
        console.warn(`⚠️ [${sessionId}] WebSocket not ready for sending data`);
        return false;
    }
    
    try {
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        ws.send(message);
        return true;
    } catch (error) {
        console.error(`❌ [${sessionId}] Error sending WebSocket data:`, error);
        return false;
    }
}

/**
 * Add item to bounded array to prevent memory leaks
 */
export function addToBoundedArray(array, item, maxSize, arrayName = 'array') {
    if (!Array.isArray(array)) {
        console.warn(`addToBoundedArray: ${arrayName} is not an array, initializing as empty array`);
        array = [];
    }
    
    array.push(item);
    
    // Remove oldest entries if limit exceeded
    if (array.length > maxSize) {
        const removed = array.splice(0, array.length - maxSize);
        console.log(`🧹 Trimmed ${removed.length} old entries from ${arrayName} (max: ${maxSize})`);
    }
    
    return array;
}

/**
 * Clean up connection data to prevent memory leaks
 */
export function cleanupConnectionData(connectionData) {
    if (!connectionData) {
        return;
    }
    
    // Close WebSocket connections
    const ws = getWebSocket(connectionData);
    if (ws && ws.readyState === 1) {
        try {
            ws.close();
        } catch (error) {
            console.warn('Error closing WebSocket during cleanup:', error);
        }
    }
    
    // Clear large arrays
    if (connectionData.conversationLog) {
        connectionData.conversationLog.length = 0;
    }
    if (connectionData.fullTranscript) {
        connectionData.fullTranscript.length = 0;
    }
    if (connectionData.speechTranscript) {
        connectionData.speechTranscript.length = 0;
    }
    
    // Clear timers
    if (connectionData.contextSaveInterval) {
        clearInterval(connectionData.contextSaveInterval);
        connectionData.contextSaveInterval = null;
    }
    
    // Clear other references
    connectionData.geminiSession = null;
    connectionData.ws = null;
    connectionData.localWs = null;
    connectionData.twilioWs = null;
}
