// Session Recovery Manager
import { Modality } from '../gemini/client.js';

export class SessionRecoveryManager {
    constructor(contextManager, healthMonitor, geminiClient) {
        this.contextManager = contextManager;
        this.healthMonitor = healthMonitor;
        this.geminiClient = geminiClient;
        this.recoveryQueue = new Map(); // Queue of sessions awaiting recovery
        this.recoveryInProgress = new Set(); // Track sessions currently being recovered
        this.maxRecoveryTime = 30000; // 30 seconds max recovery time
        this.retryTimeouts = new Map(); // Track retry timeouts for cleanup
        this.cleanupHandlers = new Map(); // Track cleanup handlers for sessions
    }

    // Attempt to recover a session with enhanced context preservation and auto-retry
    async recoverSession(callSid, reason = 'unknown', activeConnections) {
        // Use atomic check-and-set to prevent concurrent recovery attempts
        const lockKey = `recovery_${callSid}`;
        const lockAcquired = await this.acquireRecoveryLock(lockKey);
        
        if (!lockAcquired) {
            console.log(`⏳ [${callSid}] Recovery already in progress, skipping duplicate attempt`);
            return false;
        }

        // Mark recovery as in progress with atomic operation
        this.recoveryInProgress.add(callSid);

        try {
            const context = this.contextManager.getSessionContext(callSid);
            if (!context || !this.contextManager.canRecover(callSid)) {
                console.log(`❌ [${callSid}] Cannot recover session - no context or max attempts reached`);
                this.recoveryInProgress.delete(callSid);
                return false;
            }
            
            const recoveryAttempt = this.contextManager.incrementRecoveryAttempt(callSid);

            console.log(`🔄 [${callSid}] Starting enhanced auto-recovery attempt ${recoveryAttempt} (reason: ${reason})`);
            console.log(`📊 [${callSid}] Context includes: ${context.conversationState.conversationLog.length} conversation entries, ${context.conversationState.fullTranscript.length} transcript entries`);
            // Get current connection data
            const connectionData = activeConnections.get(callSid);
            if (!connectionData) {
                console.log(`❌ [${callSid}] No active connection data found for recovery`);
                return false;
            }

            // Preserve current conversation state before recovery
            const currentConversationLog = connectionData.conversationLog || [];
            const currentFullTranscript = connectionData.fullTranscript || [];
            const currentSpeechTranscript = connectionData.speechTranscript || [];

            // Mark context as being recovered with enhanced tracking
            context.recoveryInfo.lastRecoveryTime = Date.now();
            context.recoveryInfo.recoveryCount = recoveryAttempt;
            context.recoveryInfo.wasInterrupted = true;
            context.recoveryInfo.interruptionReason = reason;
            context.recoveryInfo.autoRecoveryEnabled = true;

            // Update context with latest conversation data
            context.conversationState.conversationLog = currentConversationLog;
            context.conversationState.fullTranscript = currentFullTranscript;
            context.conversationState.speechTranscript = currentSpeechTranscript;

            // Save updated context
            this.contextManager.contextStore.set(callSid, context);

            // Attempt to recreate Gemini session with recovery context
            const recoveredSession = await this.recreateGeminiSessionWithRetry(callSid, context, connectionData, activeConnections);

            if (recoveredSession) {
                // Update connection data with recovered session
                connectionData.geminiSession = recoveredSession;
                connectionData.isSessionActive = true;
                connectionData.lastRecoveryTime = Date.now();

                // Preserve conversation data in connection
                connectionData.conversationLog = currentConversationLog;
                connectionData.fullTranscript = currentFullTranscript;
                connectionData.speechTranscript = currentSpeechTranscript;

                // Send recovery notification to AI with full context
                await this.sendRecoveryNotification(callSid, recoveredSession, context);

                // Update health monitoring
                this.healthMonitor.trackConnection(callSid, 'recovered', {
                    attempt: recoveryAttempt,
                    reason,
                    conversationEntries: currentConversationLog.length,
                    transcriptEntries: currentFullTranscript.length,
                    autoRecovery: true
                });

                // Clear any pending retry timeout since recovery succeeded
                if (this.retryTimeouts.has(callSid)) {
                    clearTimeout(this.retryTimeouts.get(callSid));
                    this.retryTimeouts.delete(callSid);
                }

                // Schedule automatic health checks for this recovered session
                this.scheduleRecoveryHealthChecks(callSid, activeConnections);

                console.log(`✅ [${callSid}] Enhanced auto-recovery successful on attempt ${recoveryAttempt}`);
                console.log(`📊 [${callSid}] Preserved ${currentConversationLog.length} conversation entries and ${currentFullTranscript.length} transcript entries`);
                return true;
            } else {
                console.log(`❌ [${callSid}] Failed to recreate Gemini session during recovery`);

                // Schedule retry if we haven't exceeded max attempts
                if (this.contextManager.canRecover(callSid)) {
                    console.log(`🔄 [${callSid}] Scheduling auto-retry in 5 seconds`);
                    
                    // Clear any existing retry timeout for this session
                    if (this.retryTimeouts.has(callSid)) {
                        clearTimeout(this.retryTimeouts.get(callSid));
                    }
                    
                    // Schedule retry with cleanup tracking
                    const timeoutId = setTimeout(() => {
                        this.retryTimeouts.delete(callSid);
                        this.recoverSession(callSid, `retry_after_${reason}`, activeConnections);
                    }, 5000);
                    
                    this.retryTimeouts.set(callSid, timeoutId);
                }
                return false;
            }

        } catch (error) {
            console.error(`❌ [${callSid}] Error during enhanced session recovery:`, error);
            this.healthMonitor.trackConnection(callSid, 'failed', {
                error: error.message,
                recoveryAttempt,
                reason
            });

            // Schedule retry on error if possible
            if (this.contextManager.canRecover(callSid)) {
                console.log(`🔄 [${callSid}] Scheduling auto-retry after error in 3 seconds`);
                const retryTimeout = setTimeout(() => {
                    this.retryTimeouts.delete(callSid);
                    this.recoverSession(callSid, `error_retry_${reason}`, activeConnections);
                }, 3000);
                this.retryTimeouts.set(callSid, retryTimeout);
            }
            return false;
        } finally {
            this.recoveryInProgress.delete(callSid);
            await this.releaseRecoveryLock(lockKey);
        }
    }

    // Atomic lock acquisition using a Map-based mutex
    async acquireRecoveryLock(lockKey) {
        if (!this.recoveryLocks) {
            this.recoveryLocks = new Map();
        }
        
        // Atomic check-and-set
        const existingLock = this.recoveryLocks.get(lockKey);
        if (existingLock && Date.now() - existingLock < this.maxRecoveryTime) {
            return false; // Lock is held by another operation
        }
        
        // Acquire lock
        this.recoveryLocks.set(lockKey, Date.now());
        return true;
    }

    // Release recovery lock
    async releaseRecoveryLock(lockKey) {
        if (this.recoveryLocks) {
            this.recoveryLocks.delete(lockKey);
        }
    }

    // Clean up all resources for a session
    cleanupSession(callSid) {
        // Clear any pending retry timeouts
        if (this.retryTimeouts.has(callSid)) {
            clearTimeout(this.retryTimeouts.get(callSid));
            this.retryTimeouts.delete(callSid);
        }
        
        // Clear recovery locks
        const lockKey = `recovery_${callSid}`;
        this.releaseRecoveryLock(lockKey);
        
        // Remove from recovery tracking
        this.recoveryInProgress.delete(callSid);
        this.recoveryQueue.delete(callSid);
        
        console.log(`🧹 [${callSid}] Cleaned up recovery resources`);
    }

    // Recreate Gemini session with recovery context
    // TODO: Consider refactoring to use SessionManager.createGeminiSession() for consistency
    // Currently uses direct connection to maintain recovery-specific callback handling
    async recreateGeminiSession(callSid, context, connectionData, activeConnections) {
        try {
            const sessionConfig = context.sessionConfig;

            console.log(`🤖 [${callSid}] Recreating Gemini session with model: ${sessionConfig.model}, voice: ${sessionConfig.voice}`);

            // Create new Gemini session with enhanced error handling
            const newGeminiSession = await this.geminiClient.live.connect({
                model: sessionConfig.model,
                callbacks: {
                    onopen: () => {
                        console.log(`✅ [${callSid}] Recovered Gemini session opened successfully`);
                        this.healthMonitor.trackConnection(callSid, 'connected', { recovered: true });
                    },

                    onerror: (error) => {
                        console.error(`❌ [${callSid}] Recovered Gemini session error:`, error);
                        this.healthMonitor.trackConnection(callSid, 'failed', {
                            error: error.message,
                            recovered: true
                        });

                        // Attempt another recovery if possible
                        if (this.contextManager.canRecover(callSid)) {
                            setTimeout(() => {
                                this.recoverSession(callSid, 'session_error_after_recovery', activeConnections);
                            }, 5000);
                        }
                    },

                    onclose: () => {
                        console.log(`🔌 [${callSid}] Recovered Gemini session closed`);
                        this.healthMonitor.trackConnection(callSid, 'disconnected', { recovered: true });
                    },

                    onmessage: async (message) => {
                        // Use the same message handling logic as the original session
                        try {
                            const connectionData = activeConnections.get(callSid);
                            if (!connectionData) {
                                console.warn(`⚠️ [${callSid}] No connection data found for recovered session message handling`);
                                return;
                            }

                            // Handle audio response from Gemini
                            if (message?.serverContent?.modelTurn?.parts?.[0]?.inlineData) {
                                const audio = message.serverContent.modelTurn.parts[0].inlineData;

                                if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
                                    if (connectionData.ws && connectionData.ws.readyState === 1) { // WebSocket.OPEN
                                        try {
                                            // Import AudioProcessor dynamically to avoid circular dependency
                                            let AudioProcessor;
                                            try {
                                                const module = await import('../audio/audio-processor.js');
                                                AudioProcessor = module.AudioProcessor;
                                            } catch (importError) {
                                                console.error(`❌ [${callSid}] Failed to import AudioProcessor:`, importError);
                                                return;
                                            }
                                            const audioProcessor = new AudioProcessor();
                                            const convertedAudio = audioProcessor.convertPCMToUlaw(audio.data);
                                            const audioDelta = {
                                                event: 'media',
                                                streamSid: context.connectionState.streamSid,
                                                media: { payload: convertedAudio }
                                            };
                                            connectionData.ws.send(JSON.stringify(audioDelta));
                                            console.log(`🔊 [${callSid}] Sent recovered session audio to Twilio`);
                                        } catch (audioError) {
                                            console.error(`❌ [${callSid}] Error processing recovered session audio:`, audioError);
                                        }
                                    }
                                }
                            }

                            // Handle text responses
                            const text = message.serverContent?.modelTurn?.parts?.[0]?.text;
                            if (text) {
                                console.log(`💬 [${callSid}] Recovered session response: ${text.substring(0, 100)}...`);
                                if (connectionData.summaryRequested) {
                                    connectionData.summaryText += text;
                                }
                            }

                        } catch (error) {
                            console.error(`❌ [${callSid}] Error processing recovered session message:`, error);
                        }
                    }
                },
                config: {
                    responseModalities: [Modality.AUDIO],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: sessionConfig.voice
                            }
                        }
                    }
                },
                temperature: 1.1,
                topP: 0.95,
                topK: 40,
                maxOutputTokens: 8192
            });

            return newGeminiSession;

        } catch (error) {
            console.error(`❌ [${callSid}] Error recreating Gemini session:`, error);
            return null;
        }
    }

    // Send recovery notification to AI
    async sendRecoveryNotification(callSid, geminiSession, context) {
        try {
            const recoveryMessage = this.contextManager.getRecoveryMessage(callSid);
            if (recoveryMessage && geminiSession) {
                console.log(`📢 [${callSid}] Sending recovery notification to AI`);

                // Send recovery notification using Live API
                await geminiSession.send({
                    clientContent: {
                        turns: [{
                            role: 'user',
                            parts: [{
                                text: recoveryMessage
                            }]
                        }]
                    }
                });
            }
        } catch (error) {
            console.error(`❌ [${callSid}] Error sending recovery notification:`, error);
        }
    }

    // Check if session needs recovery
    needsRecovery(callSid, activeConnections) {
        const context = this.contextManager.getSessionContext(callSid);
        const connectionData = activeConnections.get(callSid);

        return context &&
               connectionData &&
               (!connectionData.geminiSession || !connectionData.isSessionActive) &&
               this.contextManager.canRecover(callSid);
    }

    // Get recovery status for a session
    getRecoveryStatus(callSid) {
        const context = this.contextManager.getSessionContext(callSid);
        const isRecovering = this.recoveryInProgress.has(callSid);

        return {
            canRecover: this.contextManager.canRecover(callSid),
            isRecovering,
            recoveryAttempts: context?.recoveryInfo?.recoveryCount || 0,
            wasInterrupted: context?.recoveryInfo?.wasInterrupted || false,
            lastRecoveryTime: context?.recoveryInfo?.lastRecoveryTime
        };
    }

    // Get recovery queue status
    getRecoveryQueueStatus() {
        return {
            queueSize: this.recoveryQueue.size,
            inProgress: this.recoveryInProgress.size,
            queuedSessions: Array.from(this.recoveryQueue.keys()),
            inProgressSessions: Array.from(this.recoveryInProgress)
        };
    }

    // Enhanced Gemini session recreation with retry logic
    async recreateGeminiSessionWithRetry(callSid, context, connectionData, activeConnections, maxRetries = 3) {
        let lastError = null;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`🔄 [${callSid}] Gemini session recreation attempt ${attempt}/${maxRetries}`);

                const session = await this.recreateGeminiSession(callSid, context, connectionData, activeConnections);
                if (session) {
                    console.log(`✅ [${callSid}] Gemini session recreated successfully on attempt ${attempt}`);
                    return session;
                }
            } catch (error) {
                lastError = error;
                console.warn(`⚠️ [${callSid}] Gemini session recreation attempt ${attempt} failed:`, error.message);

                if (attempt < maxRetries) {
                    const delay = attempt * 2000; // Exponential backoff: 2s, 4s, 6s
                    console.log(`⏳ [${callSid}] Waiting ${delay}ms before retry...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        console.error(`❌ [${callSid}] Failed to recreate Gemini session after ${maxRetries} attempts. Last error:`, lastError);
        return null;
    }

    // Schedule periodic health checks for recovered sessions
    scheduleRecoveryHealthChecks(callSid, activeConnections) {
        // Clear any existing health check
        if (this.recoveryHealthChecks && this.recoveryHealthChecks.has(callSid)) {
            clearInterval(this.recoveryHealthChecks.get(callSid));
        }

        if (!this.recoveryHealthChecks) {
            this.recoveryHealthChecks = new Map();
        }

        // Schedule health check every 30 seconds for recovered sessions
        const healthCheckInterval = setInterval(async () => {
            const connectionData = activeConnections.get(callSid);
            if (!connectionData || !connectionData.isSessionActive) {
                console.log(`🏥 [${callSid}] Session no longer active, stopping health checks`);
                clearInterval(healthCheckInterval);
                this.recoveryHealthChecks.delete(callSid);
                return;
            }

            // Check if Gemini session is still healthy
            if (connectionData.geminiSession) {
                try {
                    // Simple health check - try to access session properties
                    const isHealthy = typeof connectionData.geminiSession.sendClientContent === 'function';
                    if (!isHealthy) {
                        console.log(`🚨 [${callSid}] Gemini session health check failed, triggering recovery`);
                        this.recoverSession(callSid, 'health_check_failed', activeConnections);
                    } else {
                        console.log(`💚 [${callSid}] Gemini session health check passed`);
                    }
                } catch (error) {
                    console.log(`🚨 [${callSid}] Gemini session health check error, triggering recovery:`, error.message);
                    this.recoverSession(callSid, 'health_check_error', activeConnections);
                }
            }
        }, 30000); // 30 seconds

        this.recoveryHealthChecks.set(callSid, healthCheckInterval);
        console.log(`🏥 [${callSid}] Recovery health checks scheduled`);
    }

    // Clean up recovery tracking for a session
    cleanupRecovery(callSid) {
        this.recoveryQueue.delete(callSid);
        this.recoveryInProgress.delete(callSid);

        // Clean up retry timeouts
        if (this.retryTimeouts.has(callSid)) {
            clearTimeout(this.retryTimeouts.get(callSid));
            this.retryTimeouts.delete(callSid);
        }

        // Clean up health checks
        if (this.recoveryHealthChecks && this.recoveryHealthChecks.has(callSid)) {
            clearInterval(this.recoveryHealthChecks.get(callSid));
            this.recoveryHealthChecks.delete(callSid);
        }

        console.log(`🧹 [${callSid}] Recovery tracking and health checks cleaned up`);
    }

    // Clean up all recovery resources (for shutdown)
    cleanup() {
        const queueSize = this.recoveryQueue.size;
        const progressSize = this.recoveryInProgress.size;
        let healthCheckCount = 0;
        let retryTimeoutCount = 0;

        // Clear all health check intervals
        if (this.recoveryHealthChecks) {
            for (const [callSid, interval] of this.recoveryHealthChecks.entries()) {
                clearInterval(interval);
                healthCheckCount++;
            }
            this.recoveryHealthChecks.clear();
        }

        // Clear all retry timeouts to prevent memory leaks
        for (const [callSid, timeoutId] of this.retryTimeouts.entries()) {
            clearTimeout(timeoutId);
            retryTimeoutCount++;
        }
        this.retryTimeouts.clear();

        this.recoveryQueue.clear();
        this.recoveryInProgress.clear();

        console.log(`🧹 RecoveryManager: Cleared ${queueSize} queued, ${progressSize} in-progress recoveries, ${healthCheckCount} health check intervals, and ${retryTimeoutCount} retry timeouts`);
    }
}
