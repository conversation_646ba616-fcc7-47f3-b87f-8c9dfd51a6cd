/**
 * Audio Forwarding Utilities
 * Handles proper audio forwarding with sequence numbers for Twilio calls
 * Fixes the missing audio forwarding issues identified in the audit
 */

import { getTwilioWebSocket, getLocalWebSocket, safeSendWebSocket } from '../utils/websocket-utils.js';
import { logger } from '../utils/logger.js';

/**
 * Forward audio to Twilio WebSocket with proper sequence number handling
 */
export async function forwardAudioToTwilio(sessionId, audio, connectionData, audioProcessor) {
    try {
        if (!audio || !audio.data) {
            console.warn(`⚠️ [${sessionId}] No audio data to forward to Twilio`);
            return false;
        }

        const twilioWs = getTwilioWebSocket(connectionData);
        if (!twilioWs) {
            console.warn(`⚠️ [${sessionId}] No Twilio WebSocket available for audio forwarding`);
            return false;
        }

        if (!connectionData.streamSid) {
            console.warn(`⚠️ [${sessionId}] No streamSid available for Twilio audio forwarding`);
            return false;
        }

        // Convert Gemini PCM audio to μ-law format for Twilio
        const convertedAudio = audioProcessor?.convertPCMToUlaw?.(audio.data) || 
                              audioProcessor?.fallbackPCMToUlaw?.(audio.data);
        
        if (!convertedAudio) {
            console.error(`❌ [${sessionId}] Failed to convert audio for Twilio`);
            return false;
        }

        // Initialize sequence number if not exists
        if (typeof connectionData.sequenceNumber !== 'number') {
            connectionData.sequenceNumber = 0;
            console.log(`🔢 [${sessionId}] Initialized Twilio sequence number to 0`);
        }

        // Create Twilio media message with sequence number
        const audioDelta = {
            event: 'media',
            sequenceNumber: connectionData.sequenceNumber.toString(),
            streamSid: connectionData.streamSid,
            media: {
                payload: convertedAudio
            }
        };

        // Send to Twilio WebSocket
        const success = safeSendWebSocket(twilioWs, audioDelta, sessionId);
        
        if (success) {
            // Increment sequence number for next packet
            connectionData.sequenceNumber++;
            connectionData.lastAudioSent = Date.now();
            
            console.log(`✅ [${sessionId}] Audio sent to Twilio (seq: ${connectionData.sequenceNumber - 1}, size: ${convertedAudio.length})`);
            
            // Log sequence number every 10 packets for debugging
            if (connectionData.sequenceNumber % 10 === 0) {
                console.log(`📊 [${sessionId}] Twilio audio stats: ${connectionData.sequenceNumber} packets sent`);
            }
            
            return true;
        } else {
            console.error(`❌ [${sessionId}] Failed to send audio to Twilio WebSocket`);
            return false;
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Error forwarding audio to Twilio:`, error);
        return false;
    }
}

/**
 * Forward audio to local testing WebSocket
 */
export async function forwardAudioToLocal(sessionId, audio, connectionData) {
    try {
        if (!audio || !audio.data) {
            console.warn(`⚠️ [${sessionId}] No audio data to forward to local WebSocket`);
            return false;
        }

        const localWs = getLocalWebSocket(connectionData);
        if (!localWs) {
            console.warn(`⚠️ [${sessionId}] No local WebSocket available for audio forwarding`);
            return false;
        }

        // For local testing, send audio data directly (already in correct format)
        const audioMessage = {
            type: 'audio',
            audio: audio.data, // Use standardized 'audio' field name
            timestamp: Date.now(),
            sessionId: sessionId
        };

        const success = safeSendWebSocket(localWs, audioMessage, sessionId);
        
        if (success) {
            connectionData.lastAudioSent = Date.now();
            console.log(`✅ [${sessionId}] Audio sent to local WebSocket (size: ${audio.data.length})`);
            return true;
        } else {
            console.error(`❌ [${sessionId}] Failed to send audio to local WebSocket`);
            return false;
        }

    } catch (error) {
        console.error(`❌ [${sessionId}] Error forwarding audio to local WebSocket:`, error);
        return false;
    }
}

/**
 * Main audio forwarding function that routes to appropriate destination
 */
export async function forwardAudio(sessionId, audio, connectionData, audioProcessor) {
    if (!audio || !connectionData) {
        console.warn(`⚠️ [${sessionId}] Missing audio or connection data for forwarding`);
        return false;
    }

    // Determine session type and route accordingly
    const sessionType = connectionData.sessionType || 'unknown';
    const isTwilioCall = connectionData.isTwilioCall || sessionType === 'twilio_call';
    
    console.log(`🔍 [${sessionId}] Audio forwarding: sessionType=${sessionType}, isTwilioCall=${isTwilioCall}`);

    if (isTwilioCall) {
        return await forwardAudioToTwilio(sessionId, audio, connectionData, audioProcessor);
    } else {
        return await forwardAudioToLocal(sessionId, audio, connectionData);
    }
}

/**
 * Initialize audio forwarding for a session
 */
export function initializeAudioForwarding(sessionId, connectionData, sessionType) {
    if (!connectionData) {
        console.warn(`⚠️ [${sessionId}] No connection data for audio forwarding initialization`);
        return;
    }

    // Set audio forwarding properties based on session type
    if (sessionType === 'twilio_call' || connectionData.isTwilioCall) {
        connectionData.audioForwardingEnabled = true;
        connectionData.sequenceNumber = 0;
        connectionData.lastAudioSent = 0;
        console.log(`🎵 [${sessionId}] Initialized Twilio audio forwarding`);
    } else {
        connectionData.audioForwardingEnabled = true;
        connectionData.lastAudioSent = 0;
        console.log(`🎵 [${sessionId}] Initialized local audio forwarding`);
    }
}

/**
 * Get audio forwarding statistics
 */
export function getAudioForwardingStats(sessionId, connectionData) {
    if (!connectionData) {
        return null;
    }

    const stats = {
        sessionId,
        sessionType: connectionData.sessionType,
        audioForwardingEnabled: connectionData.audioForwardingEnabled || false,
        lastAudioSent: connectionData.lastAudioSent || 0,
        timeSinceLastAudio: connectionData.lastAudioSent ? Date.now() - connectionData.lastAudioSent : null
    };

    // Add Twilio-specific stats
    if (connectionData.isTwilioCall || connectionData.sessionType === 'twilio_call') {
        stats.sequenceNumber = connectionData.sequenceNumber || 0;
        stats.streamSid = connectionData.streamSid || null;
        stats.hasTwilioWs = !!(connectionData.twilioWs || connectionData.ws);
    } else {
        stats.hasLocalWs = !!(connectionData.localWs || connectionData.ws);
    }

    return stats;
}

/**
 * Cleanup audio forwarding resources
 */
export function cleanupAudioForwarding(sessionId, connectionData) {
    if (!connectionData) {
        return;
    }

    // Reset audio forwarding state
    connectionData.audioForwardingEnabled = false;
    connectionData.sequenceNumber = 0;
    connectionData.lastAudioSent = 0;
    
    console.log(`🧹 [${sessionId}] Audio forwarding cleanup completed`);
}
