# Campaign Script Loading & Audio Flow Updated Audit Report

## Executive Summary
This updated audit re-examined the campaign script loading mechanism and audio handling across all 4 flows. Some issues from the original report have been fixed, but several critical bugs remain and new issues were discovered.

## 1. Campaign Script Loading - Status Update

### Fixed Issues ✅
- **Path Traversal Vulnerability**: The undefined `sanitizedFileName` variable has been fixed in `campaign-config.js:49`

### Remaining Issues ❌

#### 1.1 Script ID Mapping Confusion (Still Present)
**Location**: `src/scripts/script-manager.js:196-209`
- Scripts 7-12 are mapped to campaigns 1-6 for incoming calls
- This creates unnecessary complexity and potential for errors
- Non-numeric IDs default to campaign 1 without proper error handling

#### 1.2 Cache Invalidation (Still Present)
**Location**: `src/config/campaign-config.js`
- Cache timeout is 300 seconds by default
- No automatic invalidation when scripts are updated
- Manual cache clearing required via API endpoints

#### 1.3 Script Preloading Performance
**Location**: `src/scripts/script-manager.js:37-61`
- Preloading blocks the event loop during startup
- No parallel loading of scripts
- Could cause startup delays with many scripts

## 2. Critical Audio Flow Issues

### 2.1 CRITICAL: Missing Audio Forwarding (All Flows)
**Description**: Audio from Gemini is not being sent back to callers/testers
**Location**: `src/session/session-manager.js:handleGeminiAudio()`
**Impact**: Callers cannot hear AI responses

The SessionManager receives audio from Gemini but lacks WebSocket access to forward it:
```javascript
// session-manager.js - Audio is received but not forwarded
handleGeminiAudio(audioData) {
    // Process audio...
    // BUT: No way to send to WebSocket!
}
```

### 2.2 Race Conditions in Session Initialization
**Locations**: 
- `src/websocket/twilio-flow-handler.js:handleMedia()`
- `src/websocket/local-testing-handler.js:handleBrowserAudio()`

**Issues**:
- Audio may arrive before session is fully initialized
- No synchronization between async operations
- Can cause first words to be lost

### 2.3 WebSocket Property Inconsistency
**Impact**: Causes audio forwarding failures across different flows
- Twilio flows: Uses `ws` property
- Local testing: Uses `localWs` property
- Browser testing: Uses `twilioWs` property

### 2.4 Memory Leaks (All Flows)
**Locations**: Multiple files
- Unbounded `conversationLog` arrays
- Unbounded `transcripts` arrays
- No cleanup of old audio buffers
**Impact**: Long calls will consume excessive memory

### 2.5 Missing Sequence Numbers for Twilio
**Location**: `src/websocket/twilio-flow-handler.js`
- Twilio requires sequence numbers for audio packets
- Current implementation doesn't track sequences
- May cause audio playback issues

## 3. Flow-Specific Issues

### 3.1 Outbound Twilio Flow
- ✅ Script loading works correctly
- ❌ No audio forwarding back to caller
- ❌ Missing sequence number tracking
- ❌ No retry logic for failed audio sends

### 3.2 Outbound Testing (Browser) Flow
- ❌ Complex script loading with unnecessary fallbacks
- ❌ Assumes browser audio is PCM when it's actually WebM/Opus
- ❌ Error recovery creates new sessions, losing context
- ❌ No audio forwarding implementation

### 3.3 Inbound Twilio Flow
- ✅ Uses correct script IDs (7-12)
- ❌ Confusing ID mapping to campaigns 1-6
- ❌ No audio forwarding back to caller
- ❌ Same memory leak issues

### 3.4 Inbound Testing (Browser) Flow
- ❌ Hardcoded to always use outbound scripts
- ❌ Cannot test incoming scenarios properly
- ❌ Missing proper script type detection
- ❌ No audio forwarding implementation

## 4. New Issues Discovered

### 4.1 Browser Audio Format Assumption
**Location**: `src/audio/audio-processor.js`
- Code assumes browser sends PCM16 audio
- Browsers actually send WebM/Opus format
- Causes audio processing failures

### 4.2 Session Recovery Flaws
**Location**: `src/session/recovery-manager.js`
- Recovery creates new Gemini sessions
- Loses all conversation context
- No state persistence mechanism

### 4.3 Config Loading Complexity
**Location**: `src/websocket/config-handlers.js`
- Multiple redundant loading paths
- Confusing fallback logic
- Potential for loading wrong scripts

## 5. Recommended Fixes (Priority Order)

### Critical (Fix Immediately)
1. **Implement Audio Forwarding**
   - Pass WebSocket reference to SessionManager
   - Or use callback pattern for audio routing
   - Or implement centralized audio router

2. **Fix WebSocket Property Access**
   - Use standardized utility functions
   - Consistent property naming across flows

### High Priority
3. **Add Synchronization**
   - Ensure session ready before processing audio
   - Queue early audio packets if needed

4. **Fix Memory Leaks**
   - Implement bounded arrays (e.g., max 1000 entries)
   - Add periodic cleanup

5. **Add Sequence Numbers**
   - Track sequence for Twilio audio packets
   - Ensure proper audio ordering

### Medium Priority
6. **Simplify Script Loading**
   - Remove confusing ID mappings
   - Use consistent loading logic
   - Fix browser testing script selection

7. **Fix Browser Audio Handling**
   - Detect actual audio format
   - Handle WebM/Opus properly

8. **Improve Error Recovery**
   - Preserve conversation state
   - Implement proper retry logic

## 6. Code Patterns to Fix

### Pattern 1: Audio Forwarding
```javascript
// Current (broken)
handleGeminiAudio(audioData) {
    // Process audio but can't send it
}

// Fixed
handleGeminiAudio(audioData, websocket) {
    // Process audio
    websocket.send(JSON.stringify({
        event: 'media',
        media: { payload: audioData }
    }));
}
```

### Pattern 2: Memory Management
```javascript
// Current (memory leak)
this.conversationLog.push(entry);

// Fixed
this.conversationLog.push(entry);
if (this.conversationLog.length > 1000) {
    this.conversationLog.shift();
}
```

### Pattern 3: Session Synchronization
```javascript
// Current (race condition)
async handleMedia(data) {
    const session = await getSession();
    session.sendAudio(data);
}

// Fixed
async handleMedia(data) {
    if (!this.sessionReady) {
        this.audioQueue.push(data);
        return;
    }
    this.session.sendAudio(data);
}
```

## 7. Testing Recommendations

1. **Test All 4 Flows**: Ensure each flow is tested independently
2. **Long Call Testing**: Test calls > 10 minutes for memory leaks
3. **Concurrent Call Testing**: Test multiple simultaneous calls
4. **Script Switching**: Test changing scripts mid-call
5. **Error Recovery**: Test network disconnections and reconnections

## 8. Conclusion

While the critical path traversal vulnerability has been fixed, the system still has major issues preventing proper audio conversations. The most critical issue is the missing audio forwarding from Gemini back to callers, affecting all flows. Additionally, memory leaks, race conditions, and configuration complexity create reliability issues.

The fixes are well-defined and implementable. Priority should be given to audio forwarding and memory management to ensure basic functionality works correctly.