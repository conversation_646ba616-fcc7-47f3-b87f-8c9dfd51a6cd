# Comprehensive Audio Flow Audit Report

## Executive Summary

This audit examined all 4 audio conversation flows in the system:
1. **Outbound Twilio flow** - `/media-stream` for outbound calls
2. **Outbound Testing (browser) flow** - `/test-outbound`
3. **Inbound Twilio flow** - `/media-stream` for inbound calls
4. **Inbound Testing (browser) flow** - `/test-inbound`

## Critical Bugs Found

### 1. Missing Audio Forwarding in SessionManager (All Flows)

**File:** `src/session/session-manager.js`
**Lines:** 260-305 (onmessage callback)
**Severity:** CRITICAL
**Impact:** No audio from Gemini reaches callers/testers

**Issue:** The SessionManager's onmessage callback receives audio from <PERSON> but doesn't forward it to the appropriate WebSocket. The `forwardAudio` function is imported but never called.

**Current Code:**
```javascript
if (audio && audio.mimeType && audio.mimeType.includes('audio')) {
    console.log(`🎵 [${callSid}] Gemini sent AUDIO response:`, {
        mimeType: audio.mimeType,
        dataLength: audio.data?.length || 0,
        hasData: !!audio.data
    });
    
    // Validate audio data before processing
    if (!audio.data || audio.data.length === 0) {
        console.warn(`⚠️ [${callSid}] Received empty audio data from Gemini`);
        return;
    }

    // Use new audio forwarding utility for proper handling
    try {
        await forwardAudio(callSid, audio, connectionData, self.audioProcessor);
    } catch (audioError) {
        console.error(`❌ [${callSid}] Error in audio forwarding:`, audioError);
    }
}
```

**Problem:** The `forwardAudio` call is there but the connectionData passed doesn't have the correct WebSocket references because SessionManager doesn't have access to the original WebSocket connections.

### 2. Race Condition in Session Initialization (All Flows)

**Files:** 
- `src/websocket/twilio-flow-handler.js` (lines 295-310)
- `src/websocket/local-testing-handler.js` (lines 336-346)

**Severity:** HIGH
**Impact:** Session might not be ready when first audio arrives

**Issue:** The session is marked as active in the onopen callback, but audio can start arriving before this callback executes.

### 3. Missing Session Config in Testing Flows

**File:** `src/websocket/config-handlers.js`
**Lines:** 102-157 (getOutboundTestConfig), 160-205 (getInboundTestConfig)
**Severity:** HIGH
**Impact:** Testing flows might not get proper campaign scripts

**Issue:** Testing config handlers have complex fallback logic but may fail to load actual campaign scripts due to timing issues with script manager initialization.

### 4. WebSocket Property Inconsistency

**Files:** All flow handlers
**Severity:** MEDIUM
**Impact:** Audio forwarding fails due to inconsistent property names

**Issue:** Different flows use different property names for WebSocket connections:
- Twilio flows: `ws`, `twilioWs`
- Testing flows: `ws`, `localWs`
- SessionManager expects: `ws`

This causes the audio forwarding to fail because it can't find the correct WebSocket.

### 5. Memory Leaks in Long-Running Sessions

**Files:** 
- `src/session/session-manager.js` (lines 326-339)
- `src/websocket/twilio-flow-handler.js` (no bounded arrays)

**Severity:** MEDIUM
**Impact:** Memory usage grows unbounded in long calls

**Issue:** The conversationLog, fullTranscript, and speechTranscript arrays grow without bounds. While SessionManager has some bounded array logic, it's not consistently applied across all flows.

### 6. Missing Error Recovery in Browser Audio Processing

**File:** `src/websocket/local-testing-handler.js`
**Lines:** 428-461 (handleAudioData)
**Severity:** MEDIUM
**Impact:** Browser audio errors can break the session

**Issue:** Browser audio processing errors trigger recovery, but the recovery mechanism might create a new session that loses the WebSocket connection context.

### 7. Incorrect Audio Format Assumptions

**File:** `src/audio/audio-processor.js`
**Lines:** 917-947 (convertWebmToPCM16)
**Severity:** MEDIUM
**Impact:** Browser audio might be processed incorrectly

**Issue:** The function assumes WebM format but browsers might send raw PCM data. The fallback logic treats raw PCM as WebM which could cause audio corruption.

### 8. Campaign Script Loading Race Conditions

**File:** `src/scripts/script-manager.js`
**Lines:** 183-259 (getScriptConfig)
**Severity:** MEDIUM
**Impact:** Sessions might start without proper AI instructions

**Issue:** Complex ID mapping logic (1-6 for outbound, 7-12 for incoming) combined with async loading can result in sessions starting with null instructions.

### 9. Missing Heartbeat in Twilio Flows

**File:** `src/websocket/twilio-flow-handler.js`
**Lines:** 312-329
**Severity:** LOW
**Impact:** Dead Twilio connections might not be detected quickly

**Issue:** Twilio WebSocket heartbeat is configured with very long timeouts (60s ping, 30s pong) and doesn't actually end the session on timeout, just marks it as having a timeout.

### 10. Duplicate Session Setup Logic

**Files:** Multiple locations
**Severity:** LOW
**Impact:** Maintenance difficulty and potential for bugs

**Issue:** Session initialization logic is duplicated across:
- `handleTwilioStartSession` in twilio-flow-handler.js
- `handleLocalStartSession` in local-testing-handler.js
- `handleStartSession` in start-session.js

Each has slightly different implementations.

## Pattern Analysis

### Common Issues Across All Flows:

1. **Audio Forwarding Architecture Flaw**: The SessionManager creates and manages Gemini sessions but doesn't have access to the client WebSockets needed for audio forwarding.

2. **Inconsistent Property Naming**: No standardized way to access WebSocket connections across different components.

3. **Missing Integration Tests**: No tests verify end-to-end audio flow from client → Gemini → client.

4. **Race Conditions**: Multiple async operations (session creation, WebSocket setup, script loading) without proper synchronization.

5. **Memory Management**: Inconsistent application of bounded arrays and cleanup logic.

## Recommendations

### Immediate Fixes:

1. **Fix Audio Forwarding**: Pass WebSocket reference to SessionManager or use a callback pattern:
```javascript
// Option 1: Pass WebSocket to SessionManager
const geminiSession = await sessionManager.createGeminiSession(callSid, config, connectionData, ws);

// Option 2: Use callback pattern
connectionData.onAudioReceived = async (audio) => {
    await forwardAudio(callSid, audio, connectionData, audioProcessor);
};
```

2. **Standardize WebSocket Access**: Use the websocket-utils.js functions consistently across all handlers.

3. **Fix Race Conditions**: Ensure session is fully initialized before processing audio:
```javascript
// Wait for session to be active
while (!connectionData.isSessionActive && retries < maxRetries) {
    await new Promise(resolve => setTimeout(resolve, 100));
    retries++;
}
```

### Long-term Improvements:

1. **Refactor Architecture**: Consider a more centralized audio routing architecture where a single component manages all audio flow.

2. **Add Integration Tests**: Create tests that verify audio flows through the entire system.

3. **Implement Proper State Management**: Use a state machine pattern for session lifecycle management.

4. **Add Monitoring**: Implement audio flow monitoring to detect when audio stops flowing in either direction.

5. **Consolidate Initialization**: Create a single, shared session initialization function used by all flows.

## Testing Recommendations

1. Test all 4 flows with:
   - Short calls (< 1 minute)
   - Long calls (> 10 minutes)
   - Rapid reconnections
   - Network interruptions

2. Monitor for:
   - Memory usage over time
   - Audio quality and latency
   - Proper cleanup on session end
   - Campaign script loading success rate

3. Add automated tests for:
   - Audio format conversions
   - WebSocket message handling
   - Session lifecycle transitions
   - Error recovery scenarios