#!/usr/bin/env node

import WebSocket from 'ws';

const WS_URL = 'wss://gemini-api.verduona.com/local-testing';

console.log('🔍 Testing session creation with enhanced debugging...');
console.log(`📡 Connecting to: ${WS_URL}`);

const ws = new WebSocket(WS_URL);

ws.on('open', () => {
    console.log('✅ WebSocket connected');
    
    // Send start session message for outbound testing
    const startMessage = {
        type: 'start-session',
        sessionType: 'outbound_testing',
        scriptId: '1',
        voice: 'Kore',
        model: 'gemini-2.5-flash-preview-native-audio-dialog'
    };
    
    console.log('📤 Sending start session message:', JSON.stringify(startMessage, null, 2));
    ws.send(JSON.stringify(startMessage));
});

ws.on('message', (data) => {
    try {
        const message = JSON.parse(data.toString());
        console.log('📥 Received message:', JSON.stringify(message, null, 2));
        
        if (message.type === 'session-started') {
            console.log('✅ Session started successfully!');
            // Keep connection open for a few seconds to see session health check
            setTimeout(() => {
                console.log('🔌 Closing connection...');
                ws.close();
            }, 10000);
        } else if (message.type === 'session-error') {
            console.error('❌ Session error:', message.error);
            ws.close();
        }
    } catch (error) {
        console.error('❌ Error parsing message:', error);
        console.log('Raw message:', data.toString());
    }
});

ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
});

ws.on('close', (code, reason) => {
    console.log(`🔌 WebSocket closed: ${code} - ${reason || 'No reason'}`);
    process.exit(0);
});

// Timeout after 30 seconds
setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
    process.exit(1);
}, 30000);
