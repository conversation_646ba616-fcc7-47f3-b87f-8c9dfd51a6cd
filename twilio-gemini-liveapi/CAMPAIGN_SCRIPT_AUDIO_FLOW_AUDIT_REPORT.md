# Campaign Script Loading & Audio Flow Audit Report

## Executive Summary
This audit examined the campaign script loading mechanism and audio handling across all 4 flows (Outbound Twilio, Outbound Testing, Inbound Twilio, Inbound Testing). Several critical bugs and inconsistencies were identified that need immediate attention.

## 1. Campaign Script Loading Analysis

### System Overview
- **Script Manager** (`src/scripts/script-manager.js`): Central script orchestration
- **Campaign Config Manager** (`src/config/campaign-config.js`): File system loading
- **Session Manager** (`src/session/session-manager.js`): Gemini session creation with scripts

### Identified Issues

#### 1.1 Path Traversal Vulnerability (CRITICAL)
**Location**: `src/config/campaign-config.js:43-46`
```javascript
if (sanitizedFileName !== fileName) {
    logger.error(`Potential path traversal attempt detected: ${fileName}`);
    return null;
}
```
**Issue**: The validation check occurs AFTER the variable `sanitizedFileName` is used, but `sanitizedFileName` is never defined in the code.
**Impact**: Potential path traversal attacks could access files outside the intended directory.

#### 1.2 Script ID Mapping Confusion
**Location**: `src/scripts/script-manager.js`
- Scripts 1-6 are for outbound campaigns
- Scripts 7-12 are for incoming campaigns but map to campaign files 1-6
**Issue**: This mapping logic is confusing and error-prone. The code subtracts 6 from incoming script IDs to get the actual campaign file number.

#### 1.3 Cache Invalidation Issues
**Location**: `src/config/campaign-config.js`
- Cache timeout is configurable but defaults to 300 seconds
- No mechanism to invalidate cache when scripts are updated
**Impact**: Script changes may not be reflected for up to 5 minutes during active calls.

## 2. Audio Flow Analysis

### 2.1 Twilio Audio Flow Issues

#### Missing Audio Forwarding Handler
**Location**: `src/websocket/twilio-flow-handler.js`
- The Twilio flow handler doesn't have a mechanism to forward Gemini audio responses back to Twilio
- Only the session manager handles audio forwarding, but it needs the streamSid which isn't consistently available

#### Sequence Number Not Used
**Location**: `src/websocket/twilio-flow-handler.js:258`
```javascript
sequenceNumber: 0, // Initialize sequence number for Twilio audio packets
```
**Issue**: The sequence number is initialized but never incremented or used when sending audio back to Twilio.

### 2.2 Browser Testing Audio Flow Issues

#### Inconsistent Audio Field Names
**Location**: `src/websocket/local-testing-handler.js:435`
```javascript
const base64Audio = data.audioData || data.audio;
```
**Issue**: The code accepts both `audioData` and `audio` fields, causing confusion about the expected format.

#### Missing Audio Response Handling in start-session.js
**Location**: `src/websocket/start-session.js:116-163`
- The `handleGeminiMessage` function has enhanced audio handling code but it's only used in one specific flow
- This code should be shared across all flows for consistency

### 2.3 Session Manager Audio Issues

#### Duplicate Audio Sending Logic
**Location**: `src/session/session-manager.js:280-317`
- Audio forwarding logic is duplicated in the onmessage callback
- Different flows (Twilio vs local) are handled within the session manager instead of by their respective handlers

#### Float32 Conversion Not Needed for Browser Audio
**Location**: `src/session/session-manager.js:495-549`
- Browser audio is already in PCM16 format but the code has fallback logic for WebM
- This suggests confusion about the expected audio format

## 3. Critical Bugs Found

### 3.1 Race Condition in Session Creation
**Location**: Multiple files
- Session metrics are initialized after the session is created
- The onopen callback might fire before metrics are ready
- This causes null reference errors in the metrics tracking

### 3.2 WebSocket State Management
**Location**: `src/websocket/twilio-flow-handler.js:126-163`
```javascript
// CRITICAL FIX: Don't end session immediately on WebSocket close
// Twilio WebSocket can close/reconnect during normal call flow
```
**Issue**: The comment indicates a fix but the implementation is incomplete. Sessions might still end prematurely.

### 3.3 Missing Error Recovery
**Location**: All flow handlers
- Recovery manager is referenced but not consistently used
- Audio processing errors don't trigger recovery in all flows
- Some flows have recovery logic, others don't

### 3.4 Inconsistent Property Names
**Issue**: WebSocket references use different property names across files:
- `ws` in some files
- `localWs` in others
- `connection.socket` vs `connection` directly

### 3.5 Memory Leaks
**Location**: `src/session/session-manager.js:336-351`
- Conversation logs are bounded to 500 entries
- But other arrays like `fullTranscript` and `speechTranscript` have no bounds
- Could cause memory issues in long-running sessions

### 3.6 Missing Heartbeat for Some Flows
- Twilio flows use 60-second heartbeat intervals
- Local testing uses 30-second intervals
- No heartbeat for the generic start-session flow

### 3.7 Incorrect Session Type Handling
**Location**: `src/websocket/start-session.js:49`
```javascript
isIncomingCall: false // Local testing is always outbound-style
```
**Issue**: This hardcodes all local testing as outbound, but the code supports both inbound and outbound testing flows.

## 4. Flow-Specific Issues

### 4.1 Outbound Twilio Flow
- No explicit audio forwarding back to Twilio
- Relies on session manager's internal forwarding which may not have all required data
- Missing streamSid in some contexts

### 4.2 Outbound Testing Flow
- Works but has inconsistent audio field naming
- No validation of audio format before sending to Gemini

### 4.3 Inbound Twilio Flow
- Same issues as outbound Twilio
- Additional issue: fallback script loading might not work correctly for inbound calls

### 4.4 Inbound Testing Flow
- Hardcoded as outbound-style in start-session.js
- May not properly simulate inbound call behavior

## 5. Recommendations

### Immediate Fixes Required:
1. Fix the undefined `sanitizedFileName` variable in campaign-config.js
2. Implement proper audio forwarding for Twilio flows with sequence number tracking
3. Standardize WebSocket property names across all files
4. Fix race condition in session metrics initialization
5. Add bounds to all transcript arrays to prevent memory leaks
6. Standardize audio field names (use `audio` consistently)
7. Fix the hardcoded `isIncomingCall: false` in start-session.js

### Medium-term Improvements:
1. Refactor audio handling to be flow-specific rather than centralized in session manager
2. Implement consistent error recovery across all flows
3. Add cache invalidation mechanism for campaign scripts
4. Simplify script ID mapping logic
5. Standardize heartbeat intervals across flows
6. Create shared audio forwarding utilities

### Long-term Recommendations:
1. Separate concerns: Move audio forwarding out of session manager
2. Create a unified flow handler base class to ensure consistency
3. Implement proper session state machine with clear transitions
4. Add comprehensive error tracking and recovery
5. Consider using TypeScript for better type safety

## 6. Security Concerns

1. **Path Traversal**: The campaign-config.js file has a critical security vulnerability
2. **No Input Validation**: Audio data is accepted without format validation
3. **Missing Rate Limiting**: No protection against rapid session creation
4. **Exposed Session IDs**: Session IDs are predictable and exposed in logs

## Conclusion

The system has several critical bugs that affect reliability and security. The most urgent issues are:
1. The undefined variable security vulnerability
2. Missing audio forwarding for Twilio calls
3. Race conditions in session initialization
4. Memory leak potential in long sessions

These issues should be addressed immediately to ensure system stability and security.